<Window x:Class="TestWPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TestWPF"
        mc:Ignorable="d"
        Title="Rx.NET 登录示例" Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="35"/>
        </Style>

        <Style TargetType="PasswordBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="35"/>
        </Style>

        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#CCCCCC"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#45a049"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="Label">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5,5,5,0"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="用户登录"
                   FontSize="24"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="20"
                   Foreground="#333"/>

        <!-- 登录表单 -->
        <Border Grid.Row="1"
                Background="White"
                CornerRadius="10"
                Margin="50,20,50,50"
                Padding="30">
            <Border.Effect>
                <DropShadowEffect Color="Gray"
                                  Direction="315"
                                  ShadowDepth="5"
                                  Opacity="0.3"
                                  BlurRadius="10"/>
            </Border.Effect>

            <StackPanel>
                <!-- 用户名 -->
                <Label Content="用户名:" FontWeight="SemiBold"/>
                <TextBox x:Name="UsernameTextBox"
                         Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                         TabIndex="1"/>

                <!-- 密码 -->
                <Label Content="密码:" FontWeight="SemiBold" Margin="5,15,5,0"/>
                <PasswordBox x:Name="PasswordBox"
                             TabIndex="2"/>

                <!-- 状态消息 -->
                <TextBlock Text="{Binding StatusMessage}"
                           Margin="5,15,5,5"
                           FontSize="12"
                           Foreground="DarkBlue"
                           TextWrapping="Wrap"
                           HorizontalAlignment="Center"/>

                <!-- 登录按钮 -->
                <Button Content="登录"
                        Command="{Binding LoginCommand}"
                        IsEnabled="{Binding IsLoginEnabled}"
                        Margin="5,20,5,5"
                        TabIndex="3"/>

                <!-- 加载指示器 -->
                <ProgressBar Height="4"
                             Margin="5,10,5,0">
                    <ProgressBar.Style>
                        <Style TargetType="ProgressBar">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Setter Property="IsIndeterminate" Value="False"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                    <Setter Property="IsIndeterminate" Value="True"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ProgressBar.Style>
                </ProgressBar>

                <!-- 提示信息 -->
                <TextBlock Text="提示: 用户名: admin, 密码: 123456"
                           FontSize="10"
                           Foreground="Gray"
                           HorizontalAlignment="Center"
                           Margin="5,20,5,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
